[package]
name = "sol-feeder"
version = "0.1.0"
edition = "2024"

[dependencies]
futures = { workspace = true }
futures-util = {workspace = true }
solana-sdk = { workspace = true }
solana-transaction-status = { workspace = true }
thiserror = {workspace = true}
tokio = { workspace = true, features = ["rt-multi-thread", "rt", "macros"], version = "1.46.1" }
tokio-stream = { workspace = true }
yellowstone-grpc-client = { workspace = true }
yellowstone-grpc-proto = { workspace = true, features = ["plugin"] }
