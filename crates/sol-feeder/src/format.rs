use solana_sdk::{pubkey::Pubkey, signature::Signature, transaction::VersionedTransaction};
use solana_transaction_status::TransactionStatusMeta;
use yellowstone_grpc_proto::convert_from::create_tx_with_meta;
use yellowstone_grpc_proto::prelude::{SubscribeUpdateTransaction};


// 数据类型枚举
#[derive(Debug, Clone)]
pub enum DataType {
    Transaction(TransactionData),
    Account(AccountData),
    Block(BlockData),
    Slot(SlotData),
}


#[derive(Debug, <PERSON>lone)]
pub struct AccountData {
    pub pubkey: String,
}

#[derive(Debug, Clone)]
pub struct AccountData1 {
    pub pubkey: String,
    pub slot: u64,
    pub lamports: u64,
    pub owner: String,
    pub data: Vec<u8>,
    pub executable: bool,
    pub rent_epoch: u64,
}

#[derive(Debug, Clone)]
pub struct BlockData {
    pub slot: u64,
    pub hash: String,
    pub parent_slot: u64,
    pub parent_hash: String,
    pub timestamp: Option<i64>,
}

#[derive(Debug, <PERSON>lone)]
pub struct SlotData {
    pub slot: u64,
    pub parent: Option<u64>,
    pub status: String,
}


#[derive(Debug, Clone)]
pub struct TransactionData {
    pub slot: u64,
    pub signature: Signature,
    #[allow(dead_code)]
    pub index: u64,
    pub meta: Option<TransactionStatusMeta>,
    #[allow(dead_code)]
    pub transaction: VersionedTransaction,
    pub account_keys: Vec<Pubkey>,
}

impl From<SubscribeUpdateTransaction> for TransactionData {
    fn from(SubscribeUpdateTransaction { transaction, slot }: SubscribeUpdateTransaction) -> Self {
        let raw = transaction.expect("should be defined");
        let index = raw.index;
        let tx = create_tx_with_meta(raw).expect("valid tx with meta");
        Self {
            slot,
            index,
            signature: *tx.transaction_signature(),
            meta: tx.get_status_meta(),
            transaction: tx.get_transaction(),
            account_keys: tx.account_keys().iter().copied().collect(),
        }
    }
}
