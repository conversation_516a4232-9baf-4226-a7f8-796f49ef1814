use std::sync::Arc;
use std::time::Duration;
use futures::channel::mpsc::SendError;
use futures_util::{Sink, SinkExt};
use thiserror::Error;
use tokio::sync::Mutex;
use tokio_stream::StreamExt;
use yellowstone_grpc_client::{ClientTlsConfig, GeyserGrpcBuilderError, GeyserGrpcClient, GeyserGrpcClientError, Interceptor};
use yellowstone_grpc_proto::geyser::{CommitmentLevel, SubscribeRequestPing};
use yellowstone_grpc_proto::geyser::subscribe_update::UpdateOneof;
use yellowstone_grpc_proto::prelude::{SubscribeRequest, SubscribeRequestFilterAccounts, SubscribeUpdate};
use crate::format::{AccountData, DataType, TransactionData};


pub struct YellowstoneGrpc {
    endpoint: String,
    x_token: Option<String>,
}

impl YellowstoneGrpc {
    pub fn new(endpoint: String, x_token: Option<String>) -> Self {
        Self { endpoint, x_token }
    }

    pub async fn build_client(
        &self,
    ) -> Result<Arc<Mutex<GeyserGrpcClient<impl Interceptor>>>, YellowstoneError> {
        let client = GeyserGrpcClient::build_from_shared(self.endpoint.clone())?
            .x_token(self.x_token.clone())?
            .tls_config(ClientTlsConfig::new().with_native_roots())?
            .connect_timeout(Duration::from_secs(10))
            .keep_alive_while_idle(true)
            .timeout(Duration::from_secs(60))
            .connect()
            .await?;
        Ok(Arc::new(Mutex::new(client)))
    }

    pub async fn subscribe_with_config(
        &self,
        config: SubscriptionConfig,
        data_handler: DataHandler,
    ) -> Result<(), YellowstoneError> {
        let client = self.build_client().await?;
        let subscribe_request = config.to_subscribe_request();

        println!("SubscribeRequest: {:?}", subscribe_request);

        let (mut subscribe_tx, mut stream) = client
            .lock()
            .await
            .subscribe_with_request(Some(subscribe_request)).await?;

        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    let res = YellowstoneGrpc::message_handler(msg, &mut subscribe_tx, data_handler).await;
                    // if let Err(e) = res {
                    //     println!("Error handling message: {:?}", e);
                    // }
                }
                Err(e) => {
                    println!("Error receiving message: {:?}", e);
                    break;
                }
            }
        }

        Ok(())
    }

    // 增加处理订阅返回消息处理函数
    pub async fn message_handler(
        message: SubscribeUpdate,
        subscribe_tx: &mut (impl Sink<SubscribeRequest, Error = SendError> + std::marker::Unpin),
        handler: DataHandler,
    ) -> Result<(), YellowstoneError> {
        match message.update_oneof {
            Some(UpdateOneof::Transaction(sut)) => {
                let transaction_pretty: TransactionData = sut.into();
                if let Err(e) = handler(DataType::Transaction(transaction_pretty)) {
                    println!("Error handling transaction data: {:?}", e);
                }
            }
            Some(UpdateOneof::Account(account)) => {
                let account = AccountData {
                    pubkey: "aaa".to_string(),
                };
                if let Err(e) = handler(DataType::Account(account)) {
                    println!("Error handling transaction data: {:?}", e);
                }
                println!("Received account update");
            }
            Some(UpdateOneof::Ping(_)) => {
                let _ = subscribe_tx
                    .send(SubscribeRequest {
                        ping: Some(SubscribeRequestPing { id: 1 }),
                        ..Default::default()
                    })
                    .await;
                println!("send ping successfully");
            }
            Some(UpdateOneof::Pong(_)) => {
                println!("service is pong: {:#?}", "1111");
            }
            _ => {
                println!("Received other update type");
            }
        }
        Ok(())
    }


}


#[derive(Error, Debug)]
pub enum YellowstoneError {
    #[error("Failed to build Yellowstone gRPC client: {0}")]
    GrpcError(#[from] GeyserGrpcBuilderError),

    #[error("gRPC status Or Failed to send subscribe request: {0}")]
    GrpcClientError(#[from] GeyserGrpcClientError),
}


// 订阅配置
#[derive(Debug, Clone)]
pub struct SubscriptionConfig {
    // 要监听的账户地址
    pub accounts: Option<Vec<String>>,
    // 要监听的程序ID
    pub programs: Option<Vec<String>>,
    pub tx_account_exclude: Option<Vec<String>>,
    pub tx_account_required: Option<Vec<String>>,
    // 是否订阅交易
    pub vote: bool,
    pub failed: bool,
}


impl Default for SubscriptionConfig {
    fn default() -> Self {
        Self {
            accounts: None,
            programs: None,
            tx_account_exclude: None,
            tx_account_required: None,
            vote: false,
            failed: false,
        }
    }
}


// 实现 订阅配置的转换
impl SubscriptionConfig {
    pub fn to_subscribe_request(&self) -> SubscribeRequest {
        let mut request = SubscribeRequest::default();
        if let Some(accounts) = &self.accounts {
            request.accounts.insert(
                "client".to_string(),
                SubscribeRequestFilterAccounts {
                    account: accounts.clone(),
                    ..Default::default()
                },
            );
        }

        if let Some(programs) = &self.programs {
            request.transactions.insert(
                "client".to_string(),
                yellowstone_grpc_proto::prelude::SubscribeRequestFilterTransactions {
                    vote: Some(self.vote),
                    failed: Some(self.failed),
                    signature: None,
                    account_include: programs.clone(),
                    account_exclude: self.tx_account_exclude.clone().unwrap_or_default(),
                    account_required: self.tx_account_required.clone().unwrap_or_default(),
                },
            );
        }
        request.set_commitment(CommitmentLevel::Processed);
        request
    }
}


// 定义订阅返回数据处理函数的类型
pub type DataHandler = fn(DataType) -> Result<(), YellowstoneError>;

