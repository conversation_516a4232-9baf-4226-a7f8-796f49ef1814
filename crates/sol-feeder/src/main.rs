use std::collections::HashMap;
use futures_util::SinkExt;
use yellowstone_grpc_proto::geyser::{CommitmentLevel, SubscribeRequest, SubscribeRequestFilterTransactions};
use yellowstone_grpc_proto::geyser::subscribe_update::UpdateOneof;
use tokio_stream::StreamExt;
use yellowstone_grpc_proto::prelude::SubscribeRequestPing;
use sol_feeder::{DataHandler, DataType, SubscriptionConfig, TransactionData, YellowstoneGrpc};

#[tokio::main]
async fn main() -> Result<(), String> {
    test_new_client().await;
    Ok(())
}


async fn test_new_client() {
    let endpoint = "https://solana-yellowstone-grpc.publicnode.com:443".to_string();
    let client = YellowstoneGrpc::new(endpoint, None);

    let addrs = vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string()];

    let subscribe_config = SubscriptionConfig {
        programs: Option::from(addrs),
        ..Default::default()
    };
    let data_handler: DataHandler = |data: DataType| {
        println!("DataHandler Received transaction data: {:?}", data);
        Ok(())
    };

    client.subscribe_with_config(subscribe_config, data_handler).await.unwrap();
    println!("Subscription established successfully");
}


async fn test_build_client() {
    let endpoint = "https://solana-yellowstone-grpc.publicnode.com:443".to_string();
    let client = YellowstoneGrpc::new(endpoint, None);
    let result = client.build_client().await;
    assert!(result.is_ok());
    let client_arc = result.unwrap();
    // let addrs = vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string()];
    let addrs = vec!["EXxDT7b7APz1r2zoPffA8mmAfjj9JnnaV9c9bY7Kpump".to_string()];

    let subscribe_request = SubscribeRequest {
        transactions: HashMap::from([(
            "client".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                signature: None,
                account_include: addrs,
                account_exclude: vec![],
                account_required: vec![],
            },
        )]),

        commitment: Some(CommitmentLevel::Processed.into()),
        ..Default::default()
    };

    println!("test");
    let sub_res = client_arc
        .lock()
        .await
        .subscribe_with_request(Some(subscribe_request))
        .await;

    assert!(sub_res.is_ok());
    println!("Subscription established successfully");
    let (mut subscribe_tx, mut stream) = sub_res.unwrap();
    while let Some(message) = stream.next().await {
        match message {
            Ok(msg) => {
                match msg.update_oneof {
                    Some(UpdateOneof::Transaction(sut)) => {
                        let transaction_pretty: TransactionData = sut.into();
                        println!("transaction: {:#?}", transaction_pretty);
                    }
                    Some(UpdateOneof::Account(account)) => {
                        println!("Received account update: {:?}", account);
                    }
                    Some(UpdateOneof::Ping(_)) => {
                        let _ = subscribe_tx
                            .send(SubscribeRequest {
                                ping: Some(SubscribeRequestPing { id: 1 }),
                                ..Default::default()
                            })
                            .await;
                        println!("send ping successfully");
                    }
                    Some(UpdateOneof::Pong(_)) => {
                        println!("service is pong: {:#?}", "1111");
                    }
                    _ => {
                        println!("Received other update type");
                    }
                }
            }
            Err(e) => {
                println!("Error receiving message: {:?}", e);
                break;
            }
        }
    }
}
