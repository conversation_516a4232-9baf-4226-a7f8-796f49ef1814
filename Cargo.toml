[workspace]
resolver = "2"
members = [
   "crates/shared",
    "crates/sol-feeder"
]



[workspace.package]
name = "echoes"
version = "0.1.0"
edition = "2024"

[workspace.dependencies]
base64 = {version = "0.22.1"}
borsh = {version = "1.5.7"}
futures = {version = "0.3.31"}
futures-util = {version = "0.3.31"}
solana-sdk = {version = "2.3.1"}
solana-transaction-status = {version = "2.3.4"}
thiserror = {version = "2.0.12"}
tokio = {version = "1.46.1"}
tokio-stream = {version = "0.1.17"}
yellowstone-grpc-client = {version = "8.0.0"}
yellowstone-grpc-proto = {version = "8.0.0"}
